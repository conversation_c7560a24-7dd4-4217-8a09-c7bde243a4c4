'use strict';

// Data needed for a later exercise
const flights =
  '_Delayed_Departure;fao93766109;txl2133758440;11:25+_Arrival;bru0943384722;fao93766109;11:45+_Delayed_Arrival;hel7439299980;fao93766109;12:05+_Departure;fao93766109;lis2323639855;12:30';

const italianFoods = new Set([
  'pasta',
  'gnocchi',
  'tomatoes',
  'olive oil',
  'garlic',
  'basil',
]);

const mexicanFoods = new Set([
  'tortillas',
  'beans',
  'rice',
  'tomatoes',
  'avocado',
  'garlic',
]);

// Data needed for first part of the section
const restaurant = {
  name: 'Classico Italiano',
  location: 'Via Angelo Tavanti 23, Firenze, Italy',
  categories: ['Italian', 'Pizzeria', 'Vegetarian', 'Organic'],
  starterMenu: ['Focaccia', 'Bruschetta', 'Garlic Bread', 'Caprese Salad'],
  mainMenu: ['Pizza', 'Pasta', 'Risotto'],

  order: function (starterIndex, mainIndex) {
    return [this.starterMenu[starterIndex], this.mainMenu[mainIndex]];
  },

  orderDeliver: function ({
    starterIndex = 1,
    mainIndex = 0,
    time = '20:00',
    address,
  }) {
    console.log(` Order received! ${this.starterMenu[starterIndex]}
      and ${this.mainMenu[mainIndex]}
      will be delivered to ${address} at ${time}`);
  },

  orderPasta: function ({ ing1, ing2, ing3 }) {
    console.log(
      `Here is your delicious pasta with ${ing1},${ing2} and ${ing3}`
    );
  },

  orderPizza: function (mainIngr, ...otherIngr) {
    console.log(mainIngr);
    console.log(otherIngr);
  },

  openingHours: {
    thu: {
      open: 12,
      close: 22,
    },
    fri: {
      open: 11,
      close: 23,
    },
    sat: {
      open: 0, // Open 24 hours
      close: 24,
      close: 24,
    },
  },
};

// const arr = [2, 3, 4];
// const a = arr[0];
// const b = arr[1];
// const c = arr[2];

// const [x, y, z] = arr;
// console.log(a, b, c);
// console.log(x, y, z);

// let [main, , secondary] = restaurant.categories;

// console.log(main, secondary);
// [main, secondary] = [secondary, main];
// console.log(main, secondary);

// const [starter, mainCourse] = restaurant.order(2, 0);

// console.log(starter, mainCourse);

//const { name, openingHours, categories } = restaurant;

// restaurant.orderDeliver({
//   time: '22:30',
//   adrress: ' Via del Sole, 21',
//   mainIndex: 2,
//   startIndex: 2,
// });

//Spread operator
// const arr = [7, 8, 9];
// const badNewArr = [1, 2, arr[0], arr[1], arr[2]];
// console.log(badNewArr);

// const newArr = [1, 2, ...arr];
// console.log(newArr);

// console.log(...newArr);

// const newMenu = [...restaurant.mainMenu, 'Gnocci'];
// console.log(newMenu);

// //Copy array
// const mainMenuCopy = [...restaurant.mainMenu];

// //Join 2 arrays
// const merge = [...restaurant.starterMenu, ...restaurant.mainMenu];
// console.log(merge);

// const ingredients = [
//   prompt(
//     "Let's make pasta! Ingredient 1?",
//     "Let's make pasta! Ingredient 2?",
//     "Let's make pasta! Ingredient 3?"
//   ),
// ];

// restaurant.orderPasta(...ingredients);

// // Objects

// const newRestaurant = { ...restaurant };
// newRestaurant.name = 'New Classico Italiano';
// console.log(newRestaurant.name);

//1) Destructuring

// Spread, because on right side og =
// const arr = [1, 2, ...[3, 4]];

// // Rest, because on left side of =
// const [a, b, ...others] = [1, 2, 3, 4, 5];

// const [pizza, , risotto, ...otherFood] = [
//   ...restaurant.mainMenu,
//   ...restaurant.starterMenu,
// ];

// console.log(pizza, risotto, otherFood);

// const { sat, ...weekdays } = restaurant.openingHours;

// console.log(weekdays);

// // 2) Functions
// const add = (...numbers) => {};
// add(2, 3);
// add(4, 5, 6, 7);
// add(8, 9, 10, 1, 2, 3, 4);
// add(5, 6, 7, 8, 9, 0, 10, 11, 12, 13);

// console.log('0' || 'Vlad'); // 0
// console.log('' || '0'); // 0
// console.log('0' - 1 || 'Vlad'); // -1
// console.log(0 || 'Vlad'); // Vlad
// console.log(0 || undefined); // undefined

// console.log('0' && 'Vlad'); // 'Vlad'
// console.log('0' - 1 && 'Vlad'); // 'Vlad'
// console.log('Vlad' && {}); // {}
// console.log(0 && 'Vlad'); // 0
// console.log(0 && undefined); // 0
// restaurant.numGuests = ' a';
// console.log(0 && restaurant.numGuests); // 0
// console.log(restaurant.numGuests && 1 > 3); //0
// const newGuests = restaurant.numGuests ?? 10;
// console.log(newGuests);

///////////////////////////////////////
// Coding Challenge #1

/* 
We're building a football betting app (soccer for my American friends 😅)!

Suppose we get data from a web service about a certain game (below). In this challenge we're gonna work with the data. So here are your tasks:

1. Create one player array for each team (variables 'players1' and 'players2')
2. The first player in any player array is the goalkeeper and the others are field players. For Bayern Munich (team 1) create one variable ('gk') with the goalkeeper's name, and one array ('fieldPlayers') with all the remaining 10 field players
3. Create an array 'allPlayers' containing all players of both teams (22 players)
4. During the game, Bayern Munich (team 1) used 3 substitute players. So create a new array ('players1Final') containing all the original team1 players plus 'Thiago', 'Coutinho' and 'Perisic'
5. Based on the game.odds object, create one variable for each odd (called 'team1', 'draw' and 'team2')
6. Write a function ('printGoals') that receives an arbitrary number of player names (NOT an array) and prints each of them to the console, along with the number of goals that were scored in total (number of player names passed in)
7. The team with the lower odd is more likely to win. Print to the console which team is more likely to win, WITHOUT using an if/else statement or the ternary operator.

TEST DATA FOR 6: Use players 'Davies', 'Muller', 'Lewandowski' and 'Kimmich'. Then, call the function again with players from game.scored

GOOD LUCK 😀
*/

const game = {
  team1: 'Bayern Munich',
  team2: 'Borrussia Dortmund',
  players: [
    [
      'Neuer',
      'Pavard',
      'Martinez',
      'Alaba',
      'Davies',
      'Kimmich',
      'Goretzka',
      'Coman',
      'Muller',
      'Gnarby',
      'Lewandowski',
    ],
    [
      'Burki',
      'Schulz',
      'Hummels',
      'Akanji',
      'Hakimi',
      'Weigl',
      'Witsel',
      'Hazard',
      'Brandt',
      'Sancho',
      'Gotze',
    ],
  ],
  score: '4:0',
  scored: ['Lewandowski', 'Gnarby', 'Lewandowski', 'Hummels'],
  date: 'Nov 9th, 2037',
  odds: {
    team1: 1.33,
    x: 3.25,
    team2: 6.5,
  },
};
// 1.
const players1 = [...game.players[0]];
const players2 = [...game.players[1]];

console.log('Players 1: ', players1);
console.log('Players 2: ', players2);

// 2.
const [gk, ...fieldPlayers] = [...game.players[0]];

console.log('goalkeeper: ', gk);
console.log('Field players: ', fieldPlayers);

// 3.
const allPlayers = [...game.players[0], ...game.players[1]];
console.log('All players:', allPlayers);

// 4.
const players1Final = [...players1, 'Thiago', 'Coutinho', 'Perisic'];
console.log(players1Final);

// 5.
const {
  odds: { team1, x: draw, team2 },
} = game;
console.log(team1, draw, team2);

// 6